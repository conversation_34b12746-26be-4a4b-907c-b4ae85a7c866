# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- 完善的项目文档和架构设计
- Docker Compose 部署配置
- 环境变量配置模板
- 系统架构图和数据流程图

### Changed
- 优化 README 文档结构
- 改进项目配置管理

### Fixed
- 修复文档中的格式问题

## [0.2.0] - 2024-01-01

### Added
- 多模式问答支持
  - LLM 基础问答
  - RAG 硬件知识库问答
  - 数据问答 (R平台)
  - 汽车知识库问答
  - 全库综合问答
  - 纯检索功能
- 流式响应支持
- Gradio Web 界面
- 完整的 RESTful API
- Redis 缓存和会话管理
- 多轮对话支持
- 意图识别服务
- 文档重排序功能
- 健康检查接口
- 日志记录和监控

### Changed
- 重构代码架构，采用模块化设计
- 优化性能，支持异步处理
- 改进错误处理机制

### Security
- 添加 API Token 认证
- 实现 CORS 安全配置
- 输入验证和清理

## [0.1.0] - 2023-12-01

### Added
- 项目初始化
- 基础框架搭建
- 核心功能原型

---

## 版本说明

### 版本号规则
- **主版本号 (Major)**: 不兼容的 API 修改
- **次版本号 (Minor)**: 向下兼容的功能性新增
- **修订号 (Patch)**: 向下兼容的问题修正

### 变更类型
- **Added**: 新增功能
- **Changed**: 对现有功能的变更
- **Deprecated**: 即将移除的功能
- **Removed**: 已移除的功能
- **Fixed**: 问题修复
- **Security**: 安全相关的修复

### 发布计划

#### v0.3.0 (计划中)
- [ ] 支持更多 LLM 模型
- [ ] 增强的缓存策略
- [ ] 性能监控面板
- [ ] 批量处理接口
- [ ] 插件系统

#### v0.4.0 (计划中)
- [ ] 多语言支持
- [ ] 高级搜索功能
- [ ] 用户权限管理
- [ ] API 版本控制
- [ ] 自动化测试覆盖

#### v1.0.0 (计划中)
- [ ] 生产环境优化
- [ ] 完整的监控体系
- [ ] 高可用部署方案
- [ ] 详细的运维文档
- [ ] 性能基准测试

---

## 贡献指南

### 如何贡献
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启 Pull Request

### 提交信息规范
使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

- `feat`: 新功能
- `fix`: 问题修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat: 添加汽车知识库问答功能
fix: 修复Redis连接超时问题
docs: 更新API文档
```

### 发布流程
1. 更新版本号
2. 更新 CHANGELOG.md
3. 创建 Git Tag
4. 发布 Release
5. 部署到生产环境

---

## 支持

如果您在使用过程中遇到问题，请：

1. 查看 [文档](docs/)
2. 搜索 [Issues](../../issues)
3. 创建新的 [Issue](../../issues/new)
4. 联系维护团队

---

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
