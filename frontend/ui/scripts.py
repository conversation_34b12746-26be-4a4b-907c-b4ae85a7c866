#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JavaScript脚本模块
包含前端交互逻辑
"""

# JavaScript代码
CUSTOM_JAVASCRIPT = """
<script>
// 全局计时器管理 - 专注于首token响应时间
window.timingManager = {
    firstTokenStartTime: null,

    // 更新首token响应时间显示
    updateFirstTokenTime: function(seconds) {
        // 通过ID查找首token响应时间输入框
        const element = document.getElementById('first-token-time-display');
        if (element) {
            const input = element.querySelector('input');
            if (input) {
                input.value = seconds.toFixed(1) + '秒';
                input.dispatchEvent(new Event('input', { bubbles: true }));
            }
        }

        // 备用方法：通过标签查找
        const textboxes = document.querySelectorAll('.gr-textbox input');
        textboxes.forEach(input => {
            const label = input.closest('.gr-textbox').querySelector('label');
            if (label && label.textContent.includes('首token响应时间')) {
                input.value = seconds.toFixed(1) + '秒';
                input.dispatchEvent(new Event('input', { bubbles: true }));
            }
        });
    },

    // 开始首token计时
    startFirstTokenTimer: function() {
        this.firstTokenStartTime = Date.now();
        this.updateFirstTokenTime(0);
    },

    // 结束首token计时
    endFirstTokenTimer: function() {
        if (this.firstTokenStartTime) {
            const elapsed = (Date.now() - this.firstTokenStartTime) / 1000;
            this.updateFirstTokenTime(elapsed);
            this.firstTokenStartTime = null;
            return elapsed;
        }
        return 0;
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 监听发送按钮点击
    document.addEventListener('click', function(e) {
        if ((e.target.textContent === '发送' || e.target.textContent === '检索') &&
            (e.target.classList.contains('primary') || e.target.classList.contains('gr-button'))) {
            // 开始首token计时
            window.timingManager.startFirstTokenTimer();
        }
    });

    // 监听内容变化，检测首token响应
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' || mutation.type === 'characterData') {
                // 检查是否有新内容出现（第一个chunk）
                const target = mutation.target;
                if (target.closest && target.closest('[data-testid="markdown"]')) {
                    // 检查是否有实际内容（不是空白）
                    const content = target.textContent || target.innerText || '';
                    if (content.trim().length > 0 && window.timingManager.firstTokenStartTime) {
                        // 结束首token计时
                        window.timingManager.endFirstTokenTimer();
                    }
                }
            }
        });
    });

    // 观察整个文档的变化
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true
    });
});
</script>
"""
