# IDP Agent 开发指南

## 开发环境搭建

### 1. 环境要求

- Python 3.9+
- Git
- Redis
- IDE (推荐 VSCode 或 PyCharm)

### 2. 项目结构

```
idp_agent/
├── api/                    # API层
│   ├── app.py             # FastAPI应用入口
│   └── routes.py          # API路由定义
├── core/                  # 核心模块
│   ├── llm_provider.py    # LLM提供者
│   └── schemas.py         # 数据模型
├── pipelines/             # 处理管道
│   ├── llm_qa.py         # LLM问答
│   ├── rag_qa.py         # RAG问答
│   ├── data_qa.py        # 数据问答
│   ├── car_qa.py         # 汽车问答
│   ├── all_qa.py         # 全库问答
│   └── search.py         # 检索功能
├── services/              # 业务服务
│   ├── search_service.py  # 搜索服务
│   ├── rerank_service.py  # 重排服务
│   ├── chat_storage_service.py # 聊天存储
│   └── redis_service.py   # Redis服务
├── config/                # 配置文件
│   ├── model_config.py    # 模型配置
│   ├── search_config.py   # 搜索配置
│   └── redis_config.py    # Redis配置
├── frontend/              # 前端代码
│   ├── gradio_app_v2.py  # Gradio应用
│   ├── ui/               # UI组件
│   ├── handlers/         # 事件处理器
│   └── api/              # API客户端
├── prompts/               # 提示词模板
├── tests/                 # 测试代码
├── utils/                 # 工具函数
├── docs/                  # 文档
├── requirements.txt       # 依赖列表
└── README.md             # 项目说明
```

### 3. 开发环境配置

```bash
# 克隆项目
git clone <repository-url>
cd idp_agent

# 创建虚拟环境
python -m venv venv
source venv/bin/activate

# 安装开发依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt

# 安装pre-commit钩子
pre-commit install

# 配置环境变量
cp .env.example .env.dev
```

## 代码规范

### 1. Python代码规范

遵循 PEP 8 标准，使用以下工具：

```bash
# 代码格式化
black .

# 导入排序
isort .

# 代码检查
flake8 .

# 类型检查
mypy .
```

### 2. 命名规范

- **文件名**: 使用下划线分隔的小写字母 (`snake_case`)
- **类名**: 使用驼峰命名 (`PascalCase`)
- **函数名**: 使用下划线分隔的小写字母 (`snake_case`)
- **常量**: 使用大写字母和下划线 (`UPPER_CASE`)
- **变量名**: 使用下划线分隔的小写字母 (`snake_case`)

### 3. 文档规范

```python
def example_function(param1: str, param2: int = 0) -> Dict[str, Any]:
    """
    函数功能描述
    
    Args:
        param1: 参数1描述
        param2: 参数2描述，默认值为0
        
    Returns:
        返回值描述
        
    Raises:
        ValueError: 异常描述
        
    Example:
        >>> result = example_function("test", 1)
        >>> print(result)
        {'status': 'success'}
    """
    pass
```

## 核心模块开发

### 1. 添加新的Pipeline

创建新的问答Pipeline：

```python
# pipelines/new_qa.py
from typing import List, Dict, Any, AsyncGenerator, Optional
from core.llm_provider import get_llm_provider
from services.search_service import SearchService
from services.rerank_service import RerankService

class NewQA:
    """新的问答Pipeline"""
    
    def __init__(self, model_id: str, request_id: str = None):
        self.model_id = model_id
        self.request_id = request_id
        self.provider = get_llm_provider(model_id, request_id)
        self.search_service = SearchService(config=NEW_SEARCH_CONFIG, request_id=request_id)
        self.rerank_service = RerankService(config=NEW_RERANK_CONFIG, request_id=request_id)
        
    async def generate_stream(
        self,
        query: str,
        user_id: str,
        history: List[Dict] = None,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """流式生成回答"""
        # 实现具体逻辑
        pass
```

### 2. 添加新的API路由

在 `api/routes.py` 中添加新路由：

```python
@router.post("/new-qa")
async def new_qa(request: NewQARequest):
    """新的问答接口"""
    try:
        pipeline = NewQA(request.model_id, request_id=request.msg_id)
        
        if request.stream:
            return StreamingResponse(
                pipeline.generate_stream(
                    query=request.query,
                    user_id=request.user_id,
                    history=request.history,
                    **request.model_dump(exclude={'query', 'user_id', 'history'})
                ),
                media_type="text/plain"
            )
        else:
            result = await pipeline.generate(
                query=request.query,
                user_id=request.user_id,
                history=request.history,
                **request.model_dump(exclude={'query', 'user_id', 'history'})
            )
            return NewQAResponse(**result)
            
    except Exception as e:
        logger.error(f"新问答处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
```

### 3. 添加新的服务

创建新的业务服务：

```python
# services/new_service.py
import asyncio
from typing import List, Dict, Any, Optional
from loguru import logger

class NewService:
    """新的业务服务"""
    
    def __init__(self, config: dict, request_id: str = None):
        self.config = config
        self.request_id = request_id
        self.logger = logger.bind(request_id=request_id)
        
    async def process(self, data: Any) -> Dict[str, Any]:
        """处理业务逻辑"""
        try:
            # 实现具体逻辑
            result = await self._do_process(data)
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"处理失败: {str(e)}")
            return {"success": False, "error": str(e)}
            
    async def _do_process(self, data: Any) -> Any:
        """具体的处理逻辑"""
        pass
```

## 前端开发

### 1. 添加新的UI组件

```python
# frontend/ui/new_component.py
import gradio as gr
from typing import Callable

class NewComponent:
    """新的UI组件"""
    
    def __init__(self):
        self.component = None
        
    def create(self) -> gr.Component:
        """创建组件"""
        with gr.Column() as component:
            # 创建UI元素
            input_box = gr.Textbox(label="输入")
            output_box = gr.Textbox(label="输出")
            submit_btn = gr.Button("提交")
            
            # 绑定事件
            submit_btn.click(
                fn=self.handle_submit,
                inputs=[input_box],
                outputs=[output_box]
            )
            
        self.component = component
        return component
        
    def handle_submit(self, input_text: str) -> str:
        """处理提交事件"""
        # 实现处理逻辑
        return f"处理结果: {input_text}"
```

### 2. 添加新的事件处理器

```python
# frontend/handlers/new_handler.py
import asyncio
from typing import AsyncGenerator, Dict, Any
from frontend.api.client import APIClient

class NewHandler:
    """新的事件处理器"""
    
    def __init__(self, api_client: APIClient, app):
        self.api_client = api_client
        self.app = app
        
    async def handle_request(
        self,
        query: str,
        user_id: str,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理请求"""
        try:
            async for chunk in self.api_client.new_qa_stream(
                query=query,
                user_id=user_id,
                **kwargs
            ):
                yield chunk
        except Exception as e:
            yield {"type": "error", "content": str(e)}
```

## 测试开发

### 1. 单元测试

```python
# tests/test_new_qa.py
import pytest
import asyncio
from pipelines.new_qa import NewQA

class TestNewQA:
    """新问答Pipeline测试"""
    
    @pytest.fixture
    def pipeline(self):
        return NewQA("test_model", "test_request")
        
    @pytest.mark.asyncio
    async def test_generate_stream(self, pipeline):
        """测试流式生成"""
        query = "测试问题"
        user_id = "test_user"
        
        results = []
        async for chunk in pipeline.generate_stream(query, user_id):
            results.append(chunk)
            
        assert len(results) > 0
        assert results[-1]["type"] == "end"
        
    @pytest.mark.asyncio
    async def test_generate(self, pipeline):
        """测试非流式生成"""
        query = "测试问题"
        user_id = "test_user"
        
        result = await pipeline.generate(query, user_id)
        
        assert "answer" in result
        assert result["success"] is True
```

### 2. 集成测试

```python
# tests/test_api_integration.py
import pytest
from fastapi.testclient import TestClient
from api.app import app

class TestAPIIntegration:
    """API集成测试"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
        
    def test_health_check(self, client):
        """测试健康检查"""
        response = client.get("/api/v1/health")
        assert response.status_code == 200
        assert response.json()["status"] == "OK"
        
    def test_llm_qa(self, client):
        """测试LLM问答"""
        data = {
            "query": "测试问题",
            "user_id": "test_user",
            "model_id": "test_model",
            "msg_id": "test_msg",
            "conversation_id": "test_conv",
            "history": [],
            "stream": False
        }
        
        response = client.post(
            "/api/v1/llm-qa",
            json=data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert response.status_code == 200
        assert response.json()["success"] is True
```

### 3. 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_new_qa.py

# 运行特定测试方法
pytest tests/test_new_qa.py::TestNewQA::test_generate_stream

# 生成覆盖率报告
pytest --cov=. --cov-report=html

# 运行性能测试
pytest tests/test_performance.py -v
```

## 配置管理

### 1. 添加新配置

```python
# config/new_config.py
import os

# 新服务配置
NEW_SERVICE_CONFIG = {
    "api_url": os.getenv("NEW_SERVICE_URL", "http://localhost:8000"),
    "timeout": int(os.getenv("NEW_SERVICE_TIMEOUT", "30")),
    "max_retries": int(os.getenv("NEW_SERVICE_MAX_RETRIES", "3")),
    "enable_cache": os.getenv("NEW_SERVICE_CACHE", "true").lower() == "true"
}

# 新模型配置
NEW_MODEL_CONFIG = {
    "model_id": os.getenv("NEW_MODEL_ID", "default_model"),
    "temperature": float(os.getenv("NEW_MODEL_TEMPERATURE", "0.7")),
    "max_tokens": int(os.getenv("NEW_MODEL_MAX_TOKENS", "2048"))
}
```

### 2. 环境变量管理

```bash
# .env.example
# 新服务配置
NEW_SERVICE_URL=http://localhost:8000
NEW_SERVICE_TIMEOUT=30
NEW_SERVICE_MAX_RETRIES=3
NEW_SERVICE_CACHE=true

# 新模型配置
NEW_MODEL_ID=new_model
NEW_MODEL_TEMPERATURE=0.7
NEW_MODEL_MAX_TOKENS=2048
```

## 调试和日志

### 1. 日志配置

```python
# 在代码中使用日志
from loguru import logger

# 绑定请求ID
logger = logger.bind(request_id=request_id)

# 记录不同级别的日志
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误")
```

### 2. 调试技巧

```python
# 使用断点调试
import pdb; pdb.set_trace()

# 使用IPython调试
import IPython; IPython.embed()

# 异步代码调试
import asyncio
import pdb

async def debug_async():
    pdb.set_trace()
    # 异步代码
    pass
```

## 性能优化

### 1. 异步编程

```python
# 并发处理
async def process_multiple_requests(requests):
    tasks = [process_single_request(req) for req in requests]
    results = await asyncio.gather(*tasks)
    return results

# 使用连接池
import aiohttp

async def make_http_request(url, data):
    async with aiohttp.ClientSession() as session:
        async with session.post(url, json=data) as response:
            return await response.json()
```

### 2. 缓存优化

```python
# Redis缓存
import redis.asyncio as redis

class CacheService:
    def __init__(self):
        self.redis = redis.Redis(host='localhost', port=6379)
        
    async def get(self, key: str):
        return await self.redis.get(key)
        
    async def set(self, key: str, value: str, expire: int = 3600):
        await self.redis.set(key, value, ex=expire)
```

## 部署和发布

### 1. 构建Docker镜像

```dockerfile
# 多阶段构建
FROM python:3.9-slim as builder

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

FROM python:3.9-slim

WORKDIR /app
COPY --from=builder /usr/local/lib/python3.9/site-packages /usr/local/lib/python3.9/site-packages
COPY . .

EXPOSE 8080
CMD ["python", "run_api.py"]
```

### 2. CI/CD配置

```yaml
# .github/workflows/ci.yml
name: CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
        
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        
    - name: Run tests
      run: pytest --cov=. --cov-report=xml
      
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

## 贡献指南

### 1. 提交代码

```bash
# 创建功能分支
git checkout -b feature/new-feature

# 提交代码
git add .
git commit -m "feat: 添加新功能"

# 推送分支
git push origin feature/new-feature

# 创建Pull Request
```

### 2. 代码审查

- 确保所有测试通过
- 代码符合规范
- 添加必要的文档
- 性能影响评估

### 3. 发布流程

1. 更新版本号
2. 更新CHANGELOG
3. 创建Release Tag
4. 部署到生产环境
