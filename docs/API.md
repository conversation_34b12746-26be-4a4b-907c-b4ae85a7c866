# IDP Agent API 文档

## 概述

IDP Agent 提供了完整的 RESTful API 接口，支持多种问答模式和检索功能。所有API都支持流式响应，提供实时的用户体验。

## 基础信息

- **Base URL**: `http://your-domain.com/api/v1`
- **认证方式**: Bearer <PERSON>ken
- **内容类型**: `application/json`
- **响应格式**: JSON / 流式JSON

## 认证

所有API请求都需要在请求头中包含认证Token：

```http
Authorization: Bearer YOUR_API_TOKEN
```

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "answer": "回答内容",
  "model_id": "qwen3_32b",
  "msg_id": "msg_123",
  "conversation_id": "conv_123",
  "references": [...]
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "error_detail": {}
}
```

## API 端点

### 1. 健康检查

#### GET /health
检查服务健康状态

**响应示例**:
```json
{
  "status": "OK",
  "timestamp": "2024-01-01T12:00:00Z",
  "components": {
    "redis": "available",
    "llm": "available"
  }
}
```

### 2. LLM问答

#### POST /llm-qa
基础的大语言模型问答，不使用知识库检索。

**请求参数**:
```json
{
  "query": "什么是人工智能？",
  "user_id": "user123",
  "model_id": "qwen3_32b",
  "msg_id": "msg123",
  "conversation_id": "conv123",
  "history": [
    {
      "query": "之前的问题",
      "content": "之前的回答"
    }
  ],
  "stream": true,
  "enable_thinking": true,
  "temperature": 0.7,
  "top_p": 0.9
}
```

**参数说明**:
- `query` (string, 必填): 用户问题
- `user_id` (string, 必填): 用户ID
- `model_id` (string, 必填): 模型ID
- `msg_id` (string, 必填): 消息ID
- `conversation_id` (string, 必填): 对话ID
- `history` (array, 必填): 对话历史
- `stream` (boolean, 可选): 是否流式响应，默认true
- `enable_thinking` (boolean, 可选): 是否启用思考过程，默认true
- `temperature` (float, 可选): 温度参数，控制随机性
- `top_p` (float, 可选): Top-p参数，控制多样性

**响应示例**:
```json
{
  "success": true,
  "answer": "人工智能是...",
  "model_id": "qwen3_32b",
  "msg_id": "msg123",
  "conversation_id": "conv123"
}
```

### 3. RAG问答 (硬工知识库)

#### POST /rag-qa
基于硬件工程知识库的RAG问答。

**请求参数**:
```json
{
  "query": "CPU的工作原理是什么？",
  "user_id": "user123",
  "model_id": "qwen3_32b",
  "msg_id": "msg123",
  "conversation_id": "conv123",
  "history": [],
  "stream": true,
  "top_k": 20,
  "top_r": 10,
  "min_score": 0.7,
  "enable_thinking": true,
  "mode": "strict",
  "temperature": 0.7,
  "top_p": 0.9
}
```

**新增参数说明**:
- `top_k` (int, 可选): 检索文档数量，默认20
- `top_r` (int, 可选): 重排后保留数量
- `min_score` (float, 可选): 最小相似度分数
- `mode` (string, 可选): 模式，"strict"或"common"

**响应示例**:
```json
{
  "success": true,
  "answer": "CPU的工作原理包括...",
  "model_id": "qwen3_32b",
  "msg_id": "msg123",
  "conversation_id": "conv123",
  "references": [
    {
      "content": "相关文档内容",
      "source": "文档来源",
      "score": 0.95,
      "metadata": {}
    }
  ]
}
```

### 4. 数据问答 (R平台)

#### POST /data-qa
基于R平台数据的问答。

**请求参数**: 与RAG问答相同

**响应示例**: 与RAG问答相同，但references来源于R平台数据

### 5. 汽车知识库问答

#### POST /car-qa
基于汽车知识库的专业问答。

**请求参数**: 与RAG问答相同

**响应示例**: 与RAG问答相同，但references来源于汽车知识库

### 6. 全库问答

#### POST /all-qa
跨多个知识库的综合问答。

**请求参数**: 与RAG问答相同

**响应示例**: 与RAG问答相同，但references可能来源于多个知识库

### 7. 检索功能

#### POST /search
纯检索功能，返回相关文档但不生成回答。

**请求参数**:
```json
{
  "query": "搜索关键词",
  "user_id": "user123",
  "top_k": 60,
  "top_r": 20,
  "min_score": 0.6
}
```

**响应示例**:
```json
{
  "success": true,
  "results": {
    "data_group": [
      {
        "content": "R平台相关内容",
        "source": "R平台",
        "score": 0.95,
        "metadata": {}
      }
    ],
    "hardware_group": [
      {
        "content": "硬件相关内容",
        "source": "硬件知识库",
        "score": 0.92,
        "metadata": {}
      }
    ]
  }
}
```

## 流式响应

所有问答API都支持流式响应。当`stream=true`时，响应将以Server-Sent Events (SSE)格式返回：

```
data: {"type": "thinking", "content": "思考过程..."}

data: {"type": "content", "content": "回答内容片段"}

data: {"type": "reference", "content": {...}}

data: {"type": "end", "content": ""}
```

**流式响应类型**:
- `thinking`: 思考过程
- `content`: 回答内容
- `reference`: 参考文档
- `end`: 响应结束

## 错误码

| 错误码 | 描述 | HTTP状态码 |
|--------|------|------------|
| INVALID_REQUEST | 请求参数错误 | 400 |
| UNAUTHORIZED | 认证失败 | 401 |
| FORBIDDEN | 权限不足 | 403 |
| NOT_FOUND | 资源不存在 | 404 |
| RATE_LIMIT_EXCEEDED | 请求频率超限 | 429 |
| INTERNAL_ERROR | 服务器内部错误 | 500 |
| SERVICE_UNAVAILABLE | 服务不可用 | 503 |
| TIMEOUT | 请求超时 | 504 |

## 使用示例

### Python示例

```python
import requests
import json

# 配置
API_BASE_URL = "http://localhost:8080/api/v1"
API_TOKEN = "your_api_token"

headers = {
    "Authorization": f"Bearer {API_TOKEN}",
    "Content-Type": "application/json"
}

# LLM问答示例
def llm_qa(query, user_id="test_user"):
    url = f"{API_BASE_URL}/llm-qa"
    data = {
        "query": query,
        "user_id": user_id,
        "model_id": "qwen3_32b",
        "msg_id": f"msg_{int(time.time())}",
        "conversation_id": f"conv_{user_id}",
        "history": [],
        "stream": False
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response.json()

# 流式问答示例
def stream_qa(query, user_id="test_user"):
    url = f"{API_BASE_URL}/rag-qa"
    data = {
        "query": query,
        "user_id": user_id,
        "model_id": "qwen3_32b",
        "msg_id": f"msg_{int(time.time())}",
        "conversation_id": f"conv_{user_id}",
        "history": [],
        "stream": True
    }
    
    response = requests.post(url, headers=headers, json=data, stream=True)
    
    for line in response.iter_lines():
        if line:
            line = line.decode('utf-8')
            if line.startswith('data: '):
                data = json.loads(line[6:])
                print(f"Type: {data['type']}, Content: {data['content']}")

# 使用示例
result = llm_qa("什么是人工智能？")
print(result)

stream_qa("CPU的工作原理是什么？")
```

### JavaScript示例

```javascript
// 基础问答
async function llmQA(query, userId = 'test_user') {
    const response = await fetch('/api/v1/llm-qa', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${API_TOKEN}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            query: query,
            user_id: userId,
            model_id: 'qwen3_32b',
            msg_id: `msg_${Date.now()}`,
            conversation_id: `conv_${userId}`,
            history: [],
            stream: false
        })
    });
    
    return await response.json();
}

// 流式问答
async function streamQA(query, userId = 'test_user') {
    const response = await fetch('/api/v1/rag-qa', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${API_TOKEN}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            query: query,
            user_id: userId,
            model_id: 'qwen3_32b',
            msg_id: `msg_${Date.now()}`,
            conversation_id: `conv_${userId}`,
            history: [],
            stream: true
        })
    });
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
            if (line.startsWith('data: ')) {
                const data = JSON.parse(line.slice(6));
                console.log(`Type: ${data.type}, Content: ${data.content}`);
            }
        }
    }
}
```

## 限制和配额

- **请求频率**: 每用户每分钟最多100次请求
- **请求大小**: 单次请求最大10MB
- **响应超时**: 默认600秒
- **并发连接**: 每用户最多10个并发连接

## 版本信息

当前API版本: v1

版本更新将通过以下方式通知：
- API响应头中的版本信息
- 文档更新通知
- 向后兼容性保证
