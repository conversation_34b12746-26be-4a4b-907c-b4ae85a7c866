# IDP Agent 部署指南

## 部署方式概览

IDP Agent 支持多种部署方式：

1. **本地开发部署** - 适用于开发和测试
2. **Docker单容器部署** - 适用于小规模生产环境
3. **Docker Compose部署** - 适用于中等规模生产环境
4. **Kubernetes部署** - 适用于大规模生产环境

## 环境要求

### 硬件要求

**最低配置**:
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 50GB 可用空间
- 网络: 稳定的互联网连接

**推荐配置**:
- CPU: 8核心或更多
- 内存: 16GB RAM或更多
- 存储: 100GB SSD
- 网络: 高带宽连接

### 软件要求

- Python 3.9+
- Docker 20.0+
- Docker Compose 2.0+
- Redis 6.0+
- Git

## 本地开发部署

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd idp_agent

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
pip install -r frontend/requirements.txt
```

### 2. 配置环境变量

```bash
# 复制配置文件模板
cp .env.example .env

# 编辑配置文件
vim .env
```

必要的环境变量：
```bash
# API配置
API_BASE_URL=http://localhost:8080/api/v1
API_ACCESS_TOKEN=your_api_token

# 模型配置
DEFAULT_MODEL=qwen3_32b
OPENAI_API_KEY=your_openai_key

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 日志配置
LOG_LEVEL=INFO
```

### 3. 启动Redis

```bash
# 使用Docker启动Redis
docker run -d --name redis -p 6379:6379 redis:6-alpine

# 或使用本地Redis服务
redis-server
```

### 4. 启动服务

```bash
# 启动API服务
python run_api.py

# 在新终端启动前端服务
python run_gradio.py
```

### 5. 验证部署

- API文档: http://localhost:8080/docs
- Web界面: http://localhost:7862
- 健康检查: http://localhost:8080/api/v1/health

## Docker单容器部署

### 1. 构建镜像

```bash
# 构建Docker镜像
docker build -t idp-agent:latest .
```

### 2. 运行容器

```bash
# 运行容器
docker run -d \
  --name idp-agent \
  -p 8080:8080 \
  -p 7862:7862 \
  -e API_ACCESS_TOKEN=your_token \
  -e REDIS_HOST=redis \
  -v $(pwd)/logs:/app/logs \
  idp-agent:latest
```

### 3. 启动Redis容器

```bash
# 启动Redis容器
docker run -d \
  --name redis \
  -p 6379:6379 \
  redis:6-alpine
```

## Docker Compose部署

### 1. 创建docker-compose.yml

```yaml
version: '3.8'

services:
  idp-agent:
    build: .
    ports:
      - "8080:8080"
      - "7862:7862"
    environment:
      - API_ACCESS_TOKEN=${API_ACCESS_TOKEN}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - redis
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - idp-agent
    restart: unless-stopped

volumes:
  redis_data:
```

### 2. 创建环境变量文件

```bash
# .env文件
API_ACCESS_TOKEN=your_secure_token
OPENAI_API_KEY=your_openai_key
DEFAULT_MODEL=qwen3_32b
LOG_LEVEL=INFO
```

### 3. 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f idp-agent
```

## Kubernetes部署

### 1. 创建命名空间

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: idp-agent
```

### 2. 创建ConfigMap

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: idp-agent-config
  namespace: idp-agent
data:
  API_BASE_URL: "http://idp-agent-service:8080/api/v1"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  LOG_LEVEL: "INFO"
```

### 3. 创建Secret

```yaml
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: idp-agent-secret
  namespace: idp-agent
type: Opaque
data:
  API_ACCESS_TOKEN: <base64-encoded-token>
  OPENAI_API_KEY: <base64-encoded-key>
```

### 4. 创建Deployment

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: idp-agent
  namespace: idp-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: idp-agent
  template:
    metadata:
      labels:
        app: idp-agent
    spec:
      containers:
      - name: idp-agent
        image: idp-agent:latest
        ports:
        - containerPort: 8080
        - containerPort: 7862
        envFrom:
        - configMapRef:
            name: idp-agent-config
        - secretRef:
            name: idp-agent-secret
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 5. 创建Service

```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: idp-agent-service
  namespace: idp-agent
spec:
  selector:
    app: idp-agent
  ports:
  - name: api
    port: 8080
    targetPort: 8080
  - name: frontend
    port: 7862
    targetPort: 7862
  type: ClusterIP
```

### 6. 部署到Kubernetes

```bash
# 应用所有配置
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f secret.yaml
kubectl apply -f deployment.yaml
kubectl apply -f service.yaml

# 检查部署状态
kubectl get pods -n idp-agent
kubectl get services -n idp-agent
```

## 生产环境配置

### 1. 安全配置

```bash
# 生成强密码
API_ACCESS_TOKEN=$(openssl rand -hex 32)

# 配置HTTPS
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem
```

### 2. 性能优化

```bash
# 调整工作进程数
UVICORN_WORKERS=4

# 配置连接池
REDIS_MAX_CONNECTIONS=100
HTTP_TIMEOUT=30
```

### 3. 监控配置

```bash
# 启用详细日志
LOG_LEVEL=DEBUG

# 配置监控端点
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30
```

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查日志
   docker logs idp-agent
   
   # 检查端口占用
   netstat -tulpn | grep :8080
   ```

2. **Redis连接失败**
   ```bash
   # 测试Redis连接
   redis-cli ping
   
   # 检查Redis配置
   docker exec -it redis redis-cli info
   ```

3. **API调用失败**
   ```bash
   # 测试API健康状态
   curl http://localhost:8080/api/v1/health
   
   # 检查认证配置
   curl -H "Authorization: Bearer $API_ACCESS_TOKEN" \
        http://localhost:8080/api/v1/health
   ```

### 日志分析

```bash
# 查看API日志
tail -f logs/api.log

# 查看前端日志
tail -f logs/frontend.log

# 查看错误日志
grep ERROR logs/*.log
```

## 备份和恢复

### 数据备份

```bash
# 备份Redis数据
docker exec redis redis-cli BGSAVE

# 备份配置文件
tar -czf config-backup.tar.gz config/

# 备份日志文件
tar -czf logs-backup.tar.gz logs/
```

### 数据恢复

```bash
# 恢复Redis数据
docker cp dump.rdb redis:/data/

# 恢复配置文件
tar -xzf config-backup.tar.gz
```

## 升级指南

### 1. 备份当前版本

```bash
# 停止服务
docker-compose down

# 备份数据
./backup.sh
```

### 2. 更新代码

```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
docker-compose build
```

### 3. 启动新版本

```bash
# 启动服务
docker-compose up -d

# 验证升级
curl http://localhost:8080/api/v1/health
```
