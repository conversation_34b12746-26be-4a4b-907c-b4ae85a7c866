# IDP Agent 系统架构文档

## 概述

IDP Agent 是一个基于大语言模型的智能问答助手系统，采用微服务架构设计，支持多种问答模式和知识库检索。系统具有高可扩展性、高可用性和高性能的特点。

## 系统架构

### 整体架构

系统采用分层架构设计，从上到下分为：

1. **用户层** - 提供多种用户接入方式
2. **前端层** - 基于 Gradio 的 Web 界面
3. **API网关层** - 基于 FastAPI 的 RESTful API
4. **核心处理层** - Pipeline 处理器和 LLM 提供者
5. **服务层** - 各种业务服务组件
6. **配置层** - 统一的配置管理
7. **数据层** - 数据存储和缓存

### 核心组件

#### 1. 前端层 (Frontend Layer)

- **Gradio Web界面**: 现代化的 Web 用户界面
- **UI组件**: 配置侧边栏、聊天界面、检索界面、历史记录
- **实时通信**: 支持 WebSocket 流式响应

#### 2. API网关层 (API Gateway Layer)

- **FastAPI应用**: 高性能的异步 Web 框架
- **路由模块**: 
  - LLM问答路由
  - RAG问答路由
  - 数据问答路由
  - 汽车问答路由
  - 全库问答路由
  - 检索路由
  - 健康检查路由
- **中间件**: 认证中间件、CORS中间件

#### 3. 核心处理层 (Core Processing Layer)

- **Pipeline模块**: 
  - LLMQA Pipeline - 基础LLM问答
  - RAGQA Pipeline - 硬件知识库RAG问答
  - DATAQA Pipeline - R平台数据问答
  - CARQA Pipeline - 汽车知识库问答
  - ALLQA Pipeline - 全库综合问答
  - SEARCH Pipeline - 纯检索功能

- **LLM提供者**: 
  - OpenAI Provider
  - Qwen Provider
  - 其他模型提供者

#### 4. 服务层 (Service Layer)

- **核心服务**:
  - 搜索服务 (Search Service)
  - 重排服务 (Rerank Service)
  - 聊天服务 (Chat Service)
  - 意图识别服务 (Intent Service)

- **存储服务**:
  - 聊天历史服务 (Chat History Service)
  - Redis服务 (Redis Service)

#### 5. 数据层 (Data Layer)

- **知识库**:
  - 硬件知识库
  - 汽车知识库
  - R平台数据库
  - 文档库

- **缓存存储**:
  - Redis缓存
  - 会话存储

- **向量数据库**:
  - 向量检索引擎

## 技术栈

### 前端技术
- **Gradio 4.0+**: Web界面框架
- **HTML/CSS/JavaScript**: 前端基础技术
- **WebSocket**: 实时通信
- **HTTP Client**: API调用

### 后端技术
- **Python 3.9+**: 主要编程语言
- **FastAPI**: Web框架
- **Uvicorn**: ASGI服务器
- **Pydantic**: 数据验证
- **AsyncIO**: 异步编程

### AI/ML技术
- **OpenAI API**: GPT模型接口
- **Qwen Models**: 千问模型
- **Embedding Models**: 向量化模型
- **Rerank Models**: 重排序模型
- **Prompt Engineering**: 提示工程

### 数据存储
- **Redis**: 缓存和会话存储
- **Vector Database**: 向量数据库
- **Knowledge Base**: 知识库存储

### 基础设施
- **Docker**: 容器化部署
- **Logging**: 日志系统
- **Configuration**: 配置管理
- **Authentication**: 身份认证

## 数据流程

### RAG问答流程

1. **用户输入**: 用户在Web界面输入问题
2. **请求路由**: API网关将请求路由到对应的Pipeline
3. **知识检索**: 
   - 调用搜索服务进行向量检索
   - 从知识库获取相关文档
   - 使用重排服务进行语义重排序
4. **上下文构建**: Pipeline构建包含检索知识的Prompt
5. **模型生成**: 调用大语言模型生成回答
6. **流式返回**: 实时流式返回生成内容
7. **历史保存**: 保存对话历史到Redis

### LLM问答流程

1. **用户输入**: 用户输入问题
2. **直接调用**: 直接调用大语言模型
3. **流式生成**: 流式返回生成内容
4. **历史保存**: 保存对话历史

## 部署架构

### 单机部署
```
┌─────────────────────────────────────┐
│           Docker Container          │
│  ┌─────────────┐  ┌─────────────┐   │
│  │   Frontend  │  │   Backend   │   │
│  │   (Gradio)  │  │  (FastAPI)  │   │
│  └─────────────┘  └─────────────┘   │
│  ┌─────────────┐  ┌─────────────┐   │
│  │    Redis    │  │  Vector DB  │   │
│  └─────────────┘  └─────────────┘   │
└─────────────────────────────────────┘
```

### 分布式部署
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Frontend   │    │   Backend   │    │   Storage   │
│  Container  │◄──►│  Container  │◄──►│  Container  │
│  (Gradio)   │    │  (FastAPI)  │    │ (Redis+VDB) │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       └───────────────────┼───────────────────┘
                           │
                  ┌─────────────┐
                  │ Load Balancer│
                  └─────────────┘
```

## 性能优化

### 1. 异步处理
- 使用 AsyncIO 进行异步编程
- 并发处理多个请求
- 非阻塞I/O操作

### 2. 缓存策略
- Redis缓存热点数据
- 会话缓存减少重复计算
- 向量检索结果缓存

### 3. 流式响应
- 实时流式输出
- 减少用户等待时间
- 提升用户体验

### 4. 负载均衡
- 多实例部署
- 请求分发
- 故障转移

## 安全性

### 1. 身份认证
- API Token认证
- 请求签名验证
- 用户权限控制

### 2. 数据安全
- 输入验证和清理
- SQL注入防护
- XSS攻击防护

### 3. 网络安全
- HTTPS加密传输
- CORS配置
- 防火墙规则

## 监控和运维

### 1. 日志系统
- 结构化日志记录
- 错误日志追踪
- 性能日志分析

### 2. 健康检查
- 服务健康状态监控
- 依赖组件检查
- 自动故障恢复

### 3. 性能监控
- 响应时间监控
- 吞吐量统计
- 资源使用监控

## 扩展性

### 1. 水平扩展
- 无状态服务设计
- 容器化部署
- 自动伸缩

### 2. 功能扩展
- 插件化架构
- 新模型接入
- 新知识库添加

### 3. 配置管理
- 环境变量配置
- 动态配置更新
- 配置版本管理
