# IDP Agent - 智能问答助手

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://www.python.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.95+-green.svg)](https://fastapi.tiangolo.com/)
[![Gradio](https://img.shields.io/badge/Gradio-4.0+-orange.svg)](https://gradio.app/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📖 项目简介

IDP Agent 是一个基于大语言模型的智能问答助手系统，支持多种问答模式和知识库检索。系统采用模块化设计，提供了完整的 RAG（检索增强生成）解决方案，支持多领域知识库问答。

### 🌟 核心特性

- **多模式问答支持**：LLM问答、RAG问答、数据问答、汽车知识库问答、全库问答
- **流式响应**：支持实时流式输出，提升用户体验
- **多知识库检索**：支持硬件知识库、R平台数据、汽车知识库等多个专业领域
- **智能重排序**：基于语义相似度的文档重排序机制
- **Web界面**：基于 Gradio 的现代化 Web 界面
- **RESTful API**：完整的 API 接口，支持第三方集成
- **对话管理**：支持多轮对话和历史记录管理
- **性能监控**：完整的日志记录和性能监控

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │   API Gateway   │    │   Core Engine   │
│   (Gradio)      │◄──►│   (FastAPI)     │◄──►│   (Pipelines)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Config Mgmt   │    │   Services      │    │   LLM Provider  │
│   (Configs)     │    │   (Search/      │    │   (OpenAI/      │
│                 │    │    Rerank)      │    │    Qwen)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Data Storage  │
                       │   (Redis/       │
                       │    Vector DB)   │
                       └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- Python 3.9+
- Redis (用于缓存和会话管理)
- 足够的内存和计算资源

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd idp_agent
```

2. **安装依赖**
```bash
# 安装后端依赖
pip install -r requirements.txt

# 安装前端依赖
pip install -r frontend/requirements.txt
```

3. **配置环境变量**
```bash
# 复制配置文件模板
cp .env.example .env

# 编辑配置文件，设置必要的环境变量
vim .env
```

4. **启动服务**

**方式一：使用脚本启动（推荐）**
```bash
# 启动 API 服务
./run.sh

# 启动前端界面
python run_gradio.py
```

**方式二：手动启动**
```bash
# 启动 API 服务
python -m uvicorn api.app:app --host 0.0.0.0 --port 8080

# 启动前端界面
python frontend/gradio_app_v2.py
```

5. **访问服务**
- API 文档：http://localhost:8080/docs
- Web 界面：http://localhost:7862

### Docker 部署

```bash
# 构建镜像
docker build -t idp-agent .

# 运行容器
docker run -d -p 8080:8080 --name idp-agent idp-agent
```

## 📚 功能模块

### 1. LLM 问答 (💬)
- 基础的大语言模型问答
- 支持多轮对话
- 无需知识库检索

### 2. 硬工知识库 (📚)
- 基于硬件工程知识库的 RAG 问答
- 支持技术文档检索
- 专业领域问答

### 3. R平台问答 (🔍)
- 基于 R 平台数据的问答
- 数据驱动的回答
- 支持数据查询和分析

### 4. 汽车知识库 (🚗)
- 汽车领域专业知识问答
- 汽车技术文档检索
- 行业专业术语支持

### 5. 全库问答 (🌐)
- 跨多个知识库的综合问答
- 智能路由到最相关的知识库
- 全面的知识覆盖

### 6. 检索功能 (🔎)
- 纯检索功能，不生成回答
- 支持多库并行检索
- 结果重排序和过滤

## 🔧 配置说明

### 主要配置文件

- `config/model_config.py` - 模型配置
- `config/search_config.py` - 检索配置
- `config/redis_config.py` - Redis 配置
- `frontend/frontend_config.py` - 前端配置

### 环境变量

```bash
# API 配置
API_BASE_URL=http://localhost:8080/api/v1

# Gradio 配置
GRADIO_HOST=0.0.0.0
GRADIO_PORT=7862
GRADIO_SHARE=false

# 模型配置
DEFAULT_MODEL=qwen3_32b

# 日志级别
LOG_LEVEL=INFO
```

## 🔌 API 接口

### 主要端点

- `POST /api/v1/llm-qa` - LLM 问答
- `POST /api/v1/rag-qa` - RAG 问答
- `POST /api/v1/data-qa` - 数据问答
- `POST /api/v1/car-qa` - 汽车知识库问答
- `POST /api/v1/all-qa` - 全库问答
- `POST /api/v1/search` - 检索接口
- `GET /api/v1/health` - 健康检查

### 请求示例

```bash
curl -X POST "http://localhost:8080/api/v1/llm-qa" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "query": "什么是人工智能？",
    "user_id": "user123",
    "model_id": "qwen3_32b",
    "msg_id": "msg123",
    "conversation_id": "conv123",
    "history": [],
    "stream": true
  }'
```

## 🧪 测试

```bash
# 运行所有测试
pytest tests/

# 运行特定测试
pytest tests/test_api.py

# 运行测试并生成覆盖率报告
pytest --cov=. tests/
```

## 📊 性能监控

系统提供了完整的性能监控和日志记录：

- **日志文件**：`log/api.log`, `log/frontend.log`
- **性能指标**：响应时间、吞吐量、错误率
- **健康检查**：`/api/v1/health` 端点

## 🔒 安全性

- API Token 认证
- CORS 配置
- 输入验证和清理
- 错误处理和日志记录

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者：[Your Name]
- 邮箱：[<EMAIL>]
- 项目链接：[Project URL]

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户。

---

**注意**：请确保在生产环境中正确配置所有安全设置和环境变量。